#!/usr/bin/env python3
"""
测试 condense_question 功能的脚本
"""

import asyncio
import sys
import os

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.services.rag_service import RAGService
from app.schemas.rag import QueryRequest, ChatMode, ChatMemory, ChatMessage
from app.core.config import Settings


async def test_condense_question():
    """测试问题改写功能"""
    print("🧪 开始测试 condense_question 功能...")
    
    # 初始化 RAG 服务
    settings = Settings()
    rag_service = RAGService(settings)
    
    # 创建聊天记忆，模拟多轮对话
    chat_memory = ChatMemory()
    chat_memory.messages = [
        ChatMessage(role="user", content="什么是函数？"),
        ChatMessage(role="assistant", content="函数是一段可重复使用的代码块，它接受输入参数，执行特定的任务，并可能返回结果。函数可以帮助我们组织代码，提高代码的可读性和可维护性。"),
        ChatMessage(role="user", content="能给我一些具体例子吗？"),
        ChatMessage(role="assistant", content="当然可以！这里有一些具体例子：\n\n1. 数学函数：计算两个数的和\n2. 字符串函数：将文本转换为大写\n3. 列表函数：查找列表中的最大值\n\n这些函数都有明确的输入和输出。")
    ]
    
    # 测试问题改写
    current_question = "具体说说你刚才说的\"具体例子\"是啥"
    
    print(f"📝 原始问题: {current_question}")
    print(f"💭 聊天历史: {len(chat_memory.messages)} 条消息")
    
    # 调用问题改写方法
    condensed_question = await rag_service._condense_question(current_question, chat_memory)
    
    print(f"🔄 改写后的问题: {condensed_question}")
    
    # 测试完整的查询流程
    print("\n🚀 测试完整查询流程...")
    
    request = QueryRequest(
        question=current_question,
        mode=ChatMode.QUERY,
        course_id="course_01",
        data_scope="course",
        session_id="test_session_condense"
    )
    
    try:
        response = await rag_service.query(request, chat_memory)
        print(f"✅ 查询成功!")
        print(f"📄 回答: {response.answer[:200]}...")
        print(f"📚 找到 {len(response.sources)} 个相关源")
        print(f"💾 更新后的聊天记忆包含 {len(response.chat_memory.messages)} 条消息")
        
        # 检查是否避免了重复添加用户消息
        user_messages = [msg for msg in response.chat_memory.messages if msg.role == "user"]
        last_user_message = user_messages[-1].content if user_messages else ""
        
        print(f"🔍 最后一条用户消息: {last_user_message}")
        
        # 统计相同用户消息的数量
        same_message_count = sum(1 for msg in user_messages if msg.content == current_question)
        print(f"📊 相同用户消息数量: {same_message_count}")
        
        if same_message_count == 1:
            print("✅ 成功避免了重复添加用户消息!")
        else:
            print("❌ 仍然存在重复的用户消息")
            
    except Exception as e:
        print(f"❌ 查询失败: {e}")


async def test_without_history():
    """测试没有聊天历史时的情况"""
    print("\n🧪 测试没有聊天历史的情况...")
    
    settings = Settings()
    rag_service = RAGService(settings)
    
    request = QueryRequest(
        question="什么是Python？",
        mode=ChatMode.QUERY,
        course_id="course_01",
        data_scope="course",
        session_id="test_session_no_history"
    )
    
    try:
        response = await rag_service.query(request, None)
        print(f"✅ 无历史查询成功!")
        print(f"📄 回答: {response.answer[:200]}...")
        print(f"💾 新建聊天记忆包含 {len(response.chat_memory.messages)} 条消息")
        
    except Exception as e:
        print(f"❌ 无历史查询失败: {e}")


if __name__ == "__main__":
    print("🎯 测试 RAG 服务的 condense_question 功能")
    print("=" * 60)
    
    asyncio.run(test_condense_question())
    asyncio.run(test_without_history())
    
    print("\n" + "=" * 60)
    print("🎉 测试完成!")

<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Data Scope 功能测试页面</title>
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css"
      rel="stylesheet"
    />
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css"
      rel="stylesheet"
    />
    <style>
      .chat-container {
        height: 400px;
        overflow-y: auto;
        border: 1px solid #dee2e6;
        border-radius: 0.375rem;
        padding: 1rem;
        background-color: #f8f9fa;
      }
      .chat-message {
        margin-bottom: 1rem;
        padding: 0.75rem;
        border-radius: 0.5rem;
      }
      .chat-message.user {
        background-color: #007bff;
        color: white;
        margin-left: 2rem;
      }
      .chat-message.assistant {
        background-color: white;
        border: 1px solid #dee2e6;
        margin-right: 2rem;
      }
      .loading {
        opacity: 0.7;
      }
      .session-info {
        background-color: #e3f2fd;
        border: 1px solid #2196f3;
        border-radius: 0.375rem;
        padding: 1rem;
        margin-bottom: 1rem;
      }
      .session-item {
        border: 1px solid #dee2e6;
        border-radius: 0.375rem;
        padding: 0.75rem;
        margin-bottom: 0.5rem;
        cursor: pointer;
        transition: all 0.2s ease;
      }
      .session-item:hover {
        background-color: #f8f9fa;
        border-color: #007bff;
      }
      .session-item.active {
        background-color: #e3f2fd;
        border-color: #007bff;
      }
      .session-title {
        font-weight: 500;
        margin-bottom: 0.25rem;
      }
      .session-meta {
        font-size: 0.875rem;
        color: #6c757d;
      }
      .session-actions {
        opacity: 0;
        transition: opacity 0.2s ease;
      }
      .session-item:hover .session-actions {
        opacity: 1;
      }
    </style>
  </head>
  <body>
    <div class="container mt-4">
      <h1 class="mb-4">
        <i class="bi bi-chat-dots"></i>
        Data Scope 功能测试页面
      </h1>

      <!-- 会话列表 -->
      <div class="row mb-4">
        <div class="col-12">
          <div class="card">
            <div
              class="card-header d-flex justify-content-between align-items-center"
            >
              <h5 class="mb-0">
                <i class="bi bi-list-ul"></i>
                会话列表
              </h5>
              <div class="btn-group">
                <button
                  class="btn btn-outline-primary btn-sm"
                  onclick="refreshSessionList()"
                >
                  <i class="bi bi-arrow-clockwise"></i>
                  刷新
                </button>
                <button
                  class="btn btn-outline-secondary btn-sm"
                  onclick="showCleanupStats()"
                >
                  <i class="bi bi-info-circle"></i>
                  清理统计
                </button>
                <button
                  class="btn btn-outline-warning btn-sm"
                  onclick="manualCleanup()"
                >
                  <i class="bi bi-trash"></i>
                  手动清理
                </button>
              </div>
            </div>
            <div class="card-body">
              <div id="session-list-container">
                <div class="text-center text-muted">
                  <i
                    class="bi bi-inbox"
                    style="font-size: 2rem; opacity: 0.3"
                  ></i>
                  <p class="mt-2">暂无会话记录</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 参数输入区域 -->
      <div class="row">
        <div class="col-md-6">
          <div class="card mb-4">
            <div class="card-header">
              <h5 class="mb-0">
                <i class="bi bi-gear"></i>
                会话参数配置
              </h5>
            </div>
            <div class="card-body">
              <form id="session-config-form">
                <!-- Data Scope 选择 -->
                <div class="mb-3">
                  <label for="data-scope" class="form-label"
                    >数据范围 (Data Scope) *</label
                  >
                  <select class="form-select" id="data-scope" required>
                    <option value="">请选择数据范围</option>
                    <option value="course">课程 (Course)</option>
                    <option value="course_material">
                      课程材料 (Course Material)
                    </option>
                  </select>
                </div>

                <!-- 课程相关字段 -->
                <div id="course-fields" style="display: none">
                  <div class="mb-3">
                    <label for="course-id" class="form-label">课程ID *</label>
                    <input
                      type="text"
                      class="form-control"
                      id="course-id"
                      placeholder="例如: CS101"
                    />
                  </div>
                  <div class="mb-3">
                    <label for="course-name" class="form-label"
                      >课程名称 *</label
                    >
                    <input
                      type="text"
                      class="form-control"
                      id="course-name"
                      placeholder="例如: Python编程基础"
                    />
                  </div>
                </div>

                <!-- 课程材料相关字段 -->
                <div id="course-material-fields" style="display: none">
                  <div class="mb-3">
                    <label for="course-material-id" class="form-label"
                      >课程材料ID *</label
                    >
                    <input
                      type="text"
                      class="form-control"
                      id="course-material-id"
                      placeholder="例如: material_001"
                    />
                  </div>
                  <div class="mb-3">
                    <label for="course-material-name" class="form-label"
                      >课程材料名称 *</label
                    >
                    <input
                      type="text"
                      class="form-control"
                      id="course-material-name"
                      placeholder="例如: 第一章 Python语法入门"
                    />
                  </div>
                </div>

                <!-- 聊天模式 -->
                <div class="mb-3">
                  <label for="chat-mode" class="form-label">聊天模式</label>
                  <select class="form-select" id="chat-mode">
                    <option value="query">检索模式 (Query)</option>
                    <option value="chat">直接聊天模式 (Chat)</option>
                  </select>
                </div>

                <button type="submit" class="btn btn-primary w-100">
                  <i class="bi bi-play-circle"></i>
                  开始聊天
                </button>
              </form>
            </div>
          </div>
        </div>

        <div class="col-md-6">
          <!-- 当前会话信息 -->
          <div id="session-info" class="session-info" style="display: none">
            <h6><i class="bi bi-info-circle"></i> 当前会话信息</h6>
            <div id="session-details"></div>
          </div>

          <!-- 聊天界面 -->
          <div class="card">
            <div class="card-header">
              <h5 class="mb-0">
                <i class="bi bi-chat-square-text"></i>
                聊天界面
              </h5>
            </div>
            <div class="card-body">
              <div id="chat-container" class="chat-container">
                <div class="text-center text-muted">
                  <i
                    class="bi bi-chat-square-dots"
                    style="font-size: 3rem; opacity: 0.3"
                  ></i>
                  <p class="mt-3">请先配置会话参数并开始聊天</p>
                </div>
              </div>

              <!-- 聊天输入 -->
              <form id="chat-form" class="mt-3" style="display: none">
                <div class="input-group">
                  <input
                    type="text"
                    class="form-control"
                    id="chat-input"
                    placeholder="输入您的问题..."
                    required
                  />
                  <button type="submit" class="btn btn-primary">
                    <i class="bi bi-send"></i>
                  </button>
                </div>
              </form>

              <!-- 操作按钮 -->
              <div id="chat-actions" class="mt-3" style="display: none">
                <button
                  class="btn btn-outline-secondary btn-sm"
                  onclick="clearChat()"
                >
                  <i class="bi bi-trash"></i> 清空聊天
                </button>
                <button
                  class="btn btn-outline-info btn-sm"
                  onclick="showSessionInfo()"
                >
                  <i class="bi bi-info-circle"></i> 会话信息
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 调试信息 -->
      <div class="row mt-4">
        <div class="col-12">
          <div class="card">
            <div class="card-header">
              <h6 class="mb-0">
                <i class="bi bi-bug"></i>
                调试信息
              </h6>
            </div>
            <div class="card-body">
              <pre id="debug-info" class="small text-muted">等待操作...</pre>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 引入必要的JavaScript文件 -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/marked@5.1.1/marked.min.js"></script>

    <!-- 引入我们的模块 -->
    <script src="js/indexedDBHelper.js"></script>
    <script src="js/sessionManager.js"></script>
    <script src="js/visitSessionManager.js"></script>
    <script src="js/api.js"></script>

    <script>
      // 全局变量
      let currentSession = null;
      const API_BASE_URL = "http://localhost:8000";

      // 页面初始化
      document.addEventListener("DOMContentLoaded", function () {
        initializePage();
      });

      async function initializePage() {
        try {
          // 初始化 IndexedDB
          await window.indexedDBHelper.initDB();
          logDebug("IndexedDB 初始化成功");

          // 绑定事件
          bindEvents();

          // 加载会话列表
          await refreshSessionList();

          logDebug("页面初始化完成");
        } catch (error) {
          logDebug("页面初始化失败: " + error.message);
          console.error("初始化错误:", error);
        }
      }

      function bindEvents() {
        // Data Scope 选择变化
        document
          .getElementById("data-scope")
          .addEventListener("change", function () {
            toggleFieldsByDataScope(this.value);
          });

        // 会话配置表单提交
        document
          .getElementById("session-config-form")
          .addEventListener("submit", handleSessionStart);

        // 聊天表单提交
        document
          .getElementById("chat-form")
          .addEventListener("submit", handleChatSubmit);
      }

      function toggleFieldsByDataScope(dataScope) {
        const courseFields = document.getElementById("course-fields");
        const courseMaterialFields = document.getElementById(
          "course-material-fields"
        );

        // 隐藏所有字段
        courseFields.style.display = "none";
        courseMaterialFields.style.display = "none";

        // 清空所有输入
        document.getElementById("course-id").value = "";
        document.getElementById("course-name").value = "";
        document.getElementById("course-material-id").value = "";
        document.getElementById("course-material-name").value = "";

        // 根据选择显示相应字段
        if (dataScope === "course") {
          courseFields.style.display = "block";
        } else if (dataScope === "course_material") {
          courseMaterialFields.style.display = "block";
        }
      }

      async function handleSessionStart(e) {
        e.preventDefault();

        try {
          const formData = getFormData();
          logDebug("开始创建/查找会话: " + JSON.stringify(formData, null, 2));

          // 验证表单数据
          if (!validateFormData(formData)) {
            return;
          }

          // 查找或创建会话
          currentSession = await window.sessionManager.findOrCreateSession(
            formData
          );

          logDebug("会话准备完成: " + JSON.stringify(currentSession, null, 2));

          // 更新UI
          showChatInterface();
          updateSessionInfo();
          loadChatHistory();
        } catch (error) {
          logDebug("会话创建失败: " + error.message);
          alert("会话创建失败: " + error.message);
          console.error("会话创建错误:", error);
        }
      }

      function getFormData() {
        const dataScope = document.getElementById("data-scope").value;
        const mode = document.getElementById("chat-mode").value;

        const formData = {
          session_type: "course_chat",
          data_scope: dataScope,
          mode: mode,
        };

        if (dataScope === "course") {
          formData.course_id = document
            .getElementById("course-id")
            .value.trim();
          formData.course_name = document
            .getElementById("course-name")
            .value.trim();
        } else if (dataScope === "course_material") {
          formData.course_material_id = document
            .getElementById("course-material-id")
            .value.trim();
          formData.course_material_name = document
            .getElementById("course-material-name")
            .value.trim();
        }

        return formData;
      }

      function validateFormData(formData) {
        if (!formData.data_scope) {
          alert("请选择数据范围");
          return false;
        }

        if (formData.data_scope === "course") {
          if (!formData.course_id || !formData.course_name) {
            alert("请填写课程ID和课程名称");
            return false;
          }
        } else if (formData.data_scope === "course_material") {
          if (!formData.course_material_id || !formData.course_material_name) {
            alert("请填写课程材料ID和课程材料名称");
            return false;
          }
        }

        return true;
      }

      function showChatInterface() {
        document.getElementById("chat-form").style.display = "block";
        document.getElementById("chat-actions").style.display = "block";
        document.getElementById("session-info").style.display = "block";
      }

      function updateSessionInfo() {
        if (!currentSession) return;

        const details = `
                <strong>会话ID:</strong> ${currentSession.session_id}<br>
                <strong>标题:</strong> ${currentSession.title}<br>
                <strong>数据范围:</strong> ${currentSession.data_scope}<br>
                <strong>创建时间:</strong> ${new Date(
                  currentSession.created_at
                ).toLocaleString()}
            `;

        document.getElementById("session-details").innerHTML = details;
      }

      function loadChatHistory() {
        const chatContainer = document.getElementById("chat-container");
        chatContainer.innerHTML = "";

        if (currentSession.frontend_session_chat_history.length === 0) {
          chatContainer.innerHTML = `
                    <div class="text-center text-muted">
                        <i class="bi bi-chat-square-dots" style="font-size: 3rem; opacity: 0.3;"></i>
                        <p class="mt-3">开始您的智能问答之旅</p>
                    </div>
                `;
        } else {
          currentSession.frontend_session_chat_history.forEach((message) => {
            addChatMessage(message.role, message.content);
          });
        }
      }

      function logDebug(message) {
        const debugInfo = document.getElementById("debug-info");
        const timestamp = new Date().toLocaleTimeString();
        debugInfo.textContent += `[${timestamp}] ${message}\n`;
        debugInfo.scrollTop = debugInfo.scrollHeight;
      }

      async function handleChatSubmit(e) {
        e.preventDefault();

        const chatInput = document.getElementById("chat-input");
        const question = chatInput.value.trim();

        if (!question || !currentSession) return;

        // 清空输入框
        chatInput.value = "";

        // 添加用户消息到界面
        addChatMessage("user", question);

        // 显示加载状态
        const loadingId = addChatMessage("assistant", "正在思考中...", true);

        try {
          // 添加到会话历史
          await window.sessionManager.addChatMessage("user", question);

          // 构建查询请求
          const queryData = window.sessionManager.buildQueryRequest(
            question,
            currentSession.mode
          );
          logDebug("发送查询请求: " + JSON.stringify(queryData, null, 2));

          // 调用RAG API
          const response = await callRAGAPI(queryData);
          logDebug("收到API响应: " + JSON.stringify(response, null, 2));

          // 移除加载消息
          removeChatMessage(loadingId);

          // 添加助手回复
          addChatMessage("assistant", response.answer);

          // 添加到会话历史
          await window.sessionManager.addChatMessage(
            "assistant",
            response.answer
          );

          // 处理响应（更新ChatSummaryBuffer等）
          await window.sessionManager.handleQueryResponse(response);
        } catch (error) {
          // 移除加载消息
          removeChatMessage(loadingId);

          // 显示错误消息
          addChatMessage(
            "assistant",
            "抱歉，处理您的问题时出现错误: " + error.message
          );
          logDebug("聊天错误: " + error.message);
          console.error("聊天错误:", error);
        }
      }

      async function callRAGAPI(queryData) {
        const response = await fetch(`${API_BASE_URL}/api/v1/rag/query`, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(queryData),
        });

        if (!response.ok) {
          throw new Error(
            `API请求失败: ${response.status} ${response.statusText}`
          );
        }

        return await response.json();
      }

      function addChatMessage(role, content, isLoading = false) {
        const chatContainer = document.getElementById("chat-container");
        const messageId =
          "msg-" + Date.now() + "-" + Math.random().toString(36).substr(2, 9);

        // 如果是第一条消息，清空欢迎信息
        const welcomeMsg = chatContainer.querySelector(
          ".text-center.text-muted"
        );
        if (welcomeMsg) {
          welcomeMsg.remove();
        }

        const messageDiv = document.createElement("div");
        messageDiv.id = messageId;
        messageDiv.className = `chat-message ${role} ${
          isLoading ? "loading" : ""
        }`;

        if (role === "assistant" && !isLoading) {
          messageDiv.innerHTML = marked.parse(content);
        } else {
          messageDiv.textContent = content;
        }

        chatContainer.appendChild(messageDiv);
        chatContainer.scrollTop = chatContainer.scrollHeight;

        return messageId;
      }

      function removeChatMessage(messageId) {
        const messageElement = document.getElementById(messageId);
        if (messageElement) {
          messageElement.remove();
        }
      }

      async function clearChat() {
        if (!currentSession) return;

        if (!confirm("确定要清空当前会话的聊天记录吗？")) {
          return;
        }

        try {
          await window.sessionManager.clearCurrentSessionHistory();
          loadChatHistory();
          logDebug("聊天记录已清空");
        } catch (error) {
          alert("清空聊天记录失败: " + error.message);
          logDebug("清空失败: " + error.message);
        }
      }

      function showSessionInfo() {
        if (!currentSession) return;

        const info = JSON.stringify(currentSession, null, 2);
        alert("当前会话信息:\n\n" + info);
      }

      // 会话列表相关函数
      async function refreshSessionList() {
        try {
          const sessions = await window.sessionManager.getSessionList({
            limit: 20,
          });
          renderSessionList(sessions);
          logDebug(`加载了 ${sessions.length} 个会话`);
        } catch (error) {
          logDebug("加载会话列表失败: " + error.message);
          console.error("会话列表错误:", error);
        }
      }

      function renderSessionList(sessions) {
        const container = document.getElementById("session-list-container");

        if (sessions.length === 0) {
          container.innerHTML = `
            <div class="text-center text-muted">
              <i class="bi bi-inbox" style="font-size: 2rem; opacity: 0.3;"></i>
              <p class="mt-2">暂无会话记录</p>
            </div>
          `;
          return;
        }

        const sessionItems = sessions
          .map((session) => {
            const isActive =
              currentSession &&
              currentSession.session_id === session.session_id;
            const messageCount = session.frontend_session_chat_history.length;
            const lastUpdate = new Date(session.updated_at).toLocaleString();

            return `
            <div class="session-item ${isActive ? "active" : ""}"
                 onclick="loadSessionFromList('${session.session_id}')">
              <div class="d-flex justify-content-between align-items-start">
                <div class="flex-grow-1">
                  <div class="session-title">${session.title}</div>
                  <div class="session-meta">
                    <span class="badge bg-secondary me-2">${
                      session.data_scope
                    }</span>
                    ${messageCount} 条消息 · ${lastUpdate}
                  </div>
                </div>
                <div class="session-actions">
                  <button class="btn btn-outline-danger btn-sm"
                          onclick="deleteSessionFromList('${
                            session.session_id
                          }', event)">
                    <i class="bi bi-trash"></i>
                  </button>
                </div>
              </div>
            </div>
          `;
          })
          .join("");

        container.innerHTML = sessionItems;
      }

      async function loadSessionFromList(sessionId) {
        try {
          const session = await window.sessionManager.loadSession(sessionId);
          if (session) {
            currentSession = session;

            // 更新UI
            showChatInterface();
            updateSessionInfo();
            loadChatHistory();

            // 更新会话列表显示
            renderSessionList(
              await window.sessionManager.getSessionList({ limit: 20 })
            );

            logDebug(`切换到会话: ${session.title}`);
          }
        } catch (error) {
          logDebug("加载会话失败: " + error.message);
          alert("加载会话失败: " + error.message);
        }
      }

      async function deleteSessionFromList(sessionId, event) {
        event.stopPropagation(); // 阻止事件冒泡

        if (!confirm("确定要删除这个会话吗？此操作不可撤销。")) {
          return;
        }

        try {
          await window.sessionManager.deleteSession(sessionId);

          // 如果删除的是当前会话，清空当前会话
          if (currentSession && currentSession.session_id === sessionId) {
            currentSession = null;
            document.getElementById("chat-form").style.display = "none";
            document.getElementById("chat-actions").style.display = "none";
            document.getElementById("session-info").style.display = "none";
            loadChatHistory(); // 清空聊天界面
          }

          // 刷新会话列表
          await refreshSessionList();

          logDebug(`删除会话: ${sessionId}`);
        } catch (error) {
          logDebug("删除会话失败: " + error.message);
          alert("删除会话失败: " + error.message);
        }
      }

      // Visit Session 管理相关函数
      function showCleanupStats() {
        try {
          const stats = window.visitSessionManager.getCleanupStats();
          const info = `
清理统计信息:

上次清理时间: ${
            stats.lastCleanupTime
              ? stats.lastCleanupTime.toLocaleString()
              : "从未清理"
          }
活跃标签页数量: ${stats.activeVisitSessionsCount}
下次清理时间: ${stats.nextCleanupDue.toLocaleString()}
清理间隔: ${stats.cleanupIntervalDays} 天

当前 Visit Session ID: ${window.visitSessionManager.getCurrentVisitSessionId()}
          `;
          alert(info);
        } catch (error) {
          alert("获取清理统计失败: " + error.message);
        }
      }

      async function manualCleanup() {
        if (
          !confirm("确定要手动清理过期会话吗？这将删除30天前的所有会话数据。")
        ) {
          return;
        }

        try {
          const deletedCount = await window.visitSessionManager.manualCleanup();
          alert(`手动清理完成！删除了 ${deletedCount} 个过期会话。`);

          // 刷新会话列表
          await refreshSessionList();

          logDebug(`手动清理完成，删除了 ${deletedCount} 个会话`);
        } catch (error) {
          alert("手动清理失败: " + error.message);
          logDebug("手动清理失败: " + error.message);
        }
      }

      // 监听清理完成事件
      window.addEventListener("sessionsCleanedUp", function (event) {
        const { deletedCount, manual } = event.detail;
        const type = manual ? "手动" : "自动";
        logDebug(`${type}清理完成，删除了 ${deletedCount} 个过期会话`);

        // 如果当前有会话列表显示，刷新它
        if (
          document.getElementById("session-list-container").children.length > 0
        ) {
          refreshSessionList();
        }
      });
    </script>
  </body>
</html>

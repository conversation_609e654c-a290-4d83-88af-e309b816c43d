/**
 * IndexedDB 管理模块
 * 实现 ChatSessionDB 数据库的创建、管理和操作
 */

class IndexedDBHelper {
  constructor() {
    this.dbName = "ChatSessionDB";
    this.dbVersion = 1;
    this.storeName = "sessions";
    this.db = null;
  }

  /**
   * 初始化数据库
   * @returns {Promise<IDBDatabase>}
   */
  async initDB() {
    if (this.db) {
      return this.db;
    }

    return new Promise((resolve, reject) => {
      const request = indexedDB.open(this.dbName, this.dbVersion);

      request.onerror = () => {
        console.error("IndexedDB 打开失败:", request.error);
        reject(request.error);
      };

      request.onsuccess = () => {
        this.db = request.result;
        console.log("IndexedDB 初始化成功");
        resolve(this.db);
      };

      request.onupgradeneeded = (event) => {
        const db = event.target.result;
        console.log("IndexedDB 升级中...");

        // 创建 sessions 对象存储
        if (!db.objectStoreNames.contains(this.storeName)) {
          const store = db.createObjectStore(this.storeName, {
            keyPath: "session_id",
          });

          // 创建索引
          store.createIndex("updated_at", "updated_at", { unique: false });
          store.createIndex("session_type", "session_type", { unique: false });
          store.createIndex("data_scope", "data_scope", { unique: false });
          store.createIndex("course_id", "course_id", { unique: false });
          store.createIndex("course_material_id", "course_material_id", {
            unique: false,
          });
          store.createIndex("title", "title", { unique: false });
          store.createIndex("created_at", "created_at", { unique: false });

          console.log("sessions 对象存储和索引创建完成");
        }
      };
    });
  }

  /**
   * 创建新会话
   * @param {Object} sessionData - 会话数据
   * @returns {Promise<string>} 会话ID
   */
  async createSession(sessionData) {
    await this.initDB();

    return new Promise((resolve, reject) => {
      const transaction = this.db.transaction([this.storeName], "readwrite");
      const store = transaction.objectStore(this.storeName);

      // 设置时间戳
      const now = Date.now();
      const session = {
        ...sessionData,
        created_at: now,
        updated_at: now,
        frontend_session_chat_history:
          sessionData.frontend_session_chat_history || [],
        chatSummaryBuffer: sessionData.chatSummaryBuffer || null,
      };

      const request = store.add(session);

      request.onsuccess = () => {
        console.log("会话创建成功:", session.session_id);
        resolve(session.session_id);
      };

      request.onerror = () => {
        console.error("会话创建失败:", request.error);
        reject(request.error);
      };
    });
  }

  /**
   * 获取指定会话
   * @param {string} sessionId - 会话ID
   * @returns {Promise<Object|null>}
   */
  async getSession(sessionId) {
    await this.initDB();

    return new Promise((resolve, reject) => {
      const transaction = this.db.transaction([this.storeName], "readonly");
      const store = transaction.objectStore(this.storeName);
      const request = store.get(sessionId);

      request.onsuccess = () => {
        resolve(request.result || null);
      };

      request.onerror = () => {
        console.error("获取会话失败:", request.error);
        reject(request.error);
      };
    });
  }

  /**
   * 根据数据范围和对应ID查找现有会话
   * @param {string} dataScope - 数据范围 ('course' 或 'course_material')
   * @param {string} scopeId - 对应的ID (course_id 或 course_material_id)
   * @returns {Promise<Object|null>}
   */
  async findSessionByDataScope(dataScope, scopeId) {
    await this.initDB();

    return new Promise((resolve, reject) => {
      const transaction = this.db.transaction([this.storeName], "readonly");
      const store = transaction.objectStore(this.storeName);

      // 根据 data_scope 确定使用哪个索引
      const indexName =
        dataScope === "course" ? "course_id" : "course_material_id";
      const index = store.index(indexName);
      const request = index.get(scopeId);

      request.onsuccess = () => {
        const result = request.result;
        // 确保找到的会话的 data_scope 匹配
        if (result && result.data_scope === dataScope) {
          resolve(result);
        } else {
          resolve(null);
        }
      };

      request.onerror = () => {
        console.error("查找会话失败:", request.error);
        reject(request.error);
      };
    });
  }

  /**
   * 更新会话数据
   * @param {string} sessionId - 会话ID
   * @param {Object} updates - 更新的数据
   * @returns {Promise<boolean>}
   */
  async updateSession(sessionId, updates) {
    await this.initDB();

    return new Promise((resolve, reject) => {
      const transaction = this.db.transaction([this.storeName], "readwrite");
      const store = transaction.objectStore(this.storeName);

      // 先获取现有会话
      const getRequest = store.get(sessionId);

      getRequest.onsuccess = () => {
        const existingSession = getRequest.result;
        if (!existingSession) {
          reject(new Error("会话不存在"));
          return;
        }

        // 合并更新数据
        const updatedSession = {
          ...existingSession,
          ...updates,
          updated_at: Date.now(),
        };

        const putRequest = store.put(updatedSession);

        putRequest.onsuccess = () => {
          console.log("会话更新成功:", sessionId);
          resolve(true);
        };

        putRequest.onerror = () => {
          console.error("会话更新失败:", putRequest.error);
          reject(putRequest.error);
        };
      };

      getRequest.onerror = () => {
        console.error("获取会话失败:", getRequest.error);
        reject(getRequest.error);
      };
    });
  }

  /**
   * 删除会话
   * @param {string} sessionId - 会话ID
   * @returns {Promise<boolean>}
   */
  async deleteSession(sessionId) {
    await this.initDB();

    return new Promise((resolve, reject) => {
      const transaction = this.db.transaction([this.storeName], "readwrite");
      const store = transaction.objectStore(this.storeName);
      const request = store.delete(sessionId);

      request.onsuccess = () => {
        console.log("会话删除成功:", sessionId);
        resolve(true);
      };

      request.onerror = () => {
        console.error("会话删除失败:", request.error);
        reject(request.error);
      };
    });
  }

  /**
   * 清空指定会话的聊天历史和 ChatSummaryBuffer
   * @param {string} sessionId - 会话ID
   * @returns {Promise<boolean>}
   */
  async clearSessionHistory(sessionId) {
    const updates = {
      frontend_session_chat_history: [],
      chatSummaryBuffer: {
        messages: [],
        summary: null,
        token_count: 0,
      },
    };
    return await this.updateSession(sessionId, updates);
  }

  /**
   * 获取会话列表（按 updated_at 倒序排序）
   * @param {Object} options - 查询选项
   * @param {number} options.limit - 限制数量
   * @param {string} options.sessionType - 会话类型过滤
   * @param {string} options.dataScope - 数据范围过滤
   * @returns {Promise<Array>}
   */
  async listSessions(options = {}) {
    await this.initDB();

    return new Promise((resolve, reject) => {
      const transaction = this.db.transaction([this.storeName], "readonly");
      const store = transaction.objectStore(this.storeName);
      const index = store.index("updated_at");

      // 使用游标按 updated_at 倒序遍历
      const request = index.openCursor(null, "prev");
      const sessions = [];
      let count = 0;

      // 设置超时处理
      const timeoutId = setTimeout(() => {
        console.warn("获取会话列表超时");
        resolve(sessions); // 返回已获取的部分结果
      }, 5000);

      request.onsuccess = (event) => {
        const cursor = event.target.result;
        if (cursor && (!options.limit || count < options.limit)) {
          const session = cursor.value;

          // 应用过滤条件
          let include = true;
          if (
            options.sessionType &&
            session.session_type !== options.sessionType
          ) {
            include = false;
          }
          if (options.dataScope && session.data_scope !== options.dataScope) {
            include = false;
          }

          if (include) {
            sessions.push(session);
            count++;
          }

          cursor.continue();
        } else {
          clearTimeout(timeoutId);
          resolve(sessions);
        }
      };

      request.onerror = () => {
        clearTimeout(timeoutId);
        console.error("获取会话列表失败:", request.error);
        reject(request.error);
      };
    });
  }

  /**
   * 清理过期会话（超过指定天数未更新）
   * @param {number} days - 天数
   * @returns {Promise<number>} 清理的会话数量
   */
  async cleanupExpiredSessions(days = 30) {
    await this.initDB();

    const cutoffTime = Date.now() - days * 24 * 60 * 60 * 1000;
    let deletedCount = 0;

    return new Promise((resolve, reject) => {
      const transaction = this.db.transaction([this.storeName], "readwrite");
      const store = transaction.objectStore(this.storeName);
      const index = store.index("updated_at");

      // 查找过期的会话
      const range = IDBKeyRange.upperBound(cutoffTime);
      const request = index.openCursor(range);

      request.onsuccess = (event) => {
        const cursor = event.target.result;
        if (cursor) {
          cursor.delete();
          deletedCount++;
          cursor.continue();
        } else {
          console.log(`清理了 ${deletedCount} 个过期会话`);
          resolve(deletedCount);
        }
      };

      request.onerror = () => {
        console.error("清理过期会话失败:", request.error);
        reject(request.error);
      };
    });
  }

  /**
   * 根据会话标题关键词搜索会话
   * @param {string} titleKeyword - 标题关键词
   * @returns {Promise<Array>}
   */
  async searchSessionsByTitle(titleKeyword) {
    const sessions = await this.listSessions();
    return sessions.filter(
      (session) =>
        session.title &&
        session.title.toLowerCase().includes(titleKeyword.toLowerCase())
    );
  }
}

// 创建全局实例
window.indexedDBHelper = new IndexedDBHelper();

// 导出类（如果使用模块系统）
if (typeof module !== "undefined" && module.exports) {
  module.exports = IndexedDBHelper;
}

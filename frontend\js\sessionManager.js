/**
 * 会话管理模块
 * 实现基于 data_scope 的会话唯一性检查、会话创建和查找功能
 */

class SessionManager {
  constructor() {
    this.currentSession = null;
    this.dbHelper = window.indexedDBHelper;
  }

  /**
   * 生成唯一的会话ID (类似 nanoid)
   * @param {number} size - ID长度，默认21
   * @returns {string}
   */
  generateSessionId(size = 21) {
    const alphabet =
      "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";
    let id = "";
    for (let i = 0; i < size; i++) {
      id += alphabet[Math.floor(Math.random() * alphabet.length)];
    }
    return id;
  }

  /**
   * 生成会话标题
   * @param {string} sessionType - 会话类型
   * @param {string} dataScope - 数据范围
   * @param {string} courseName - 课程名称
   * @param {string} courseMaterialName - 课程材料名称
   * @param {string} firstMessage - 首条消息（用于free_chat）
   * @returns {string}
   */
  generateSessionTitle(
    sessionType,
    dataScope,
    courseName = "",
    courseMaterialName = "",
    firstMessage = ""
  ) {
    if (sessionType === "course_chat") {
      if (dataScope === "course") {
        return `课程：${courseName}`;
      } else if (dataScope === "course_material") {
        return `课件：${courseMaterialName}`;
      }
    } else if (sessionType === "free_chat") {
      // 使用首条用户消息的前20个字符
      const truncated = firstMessage.substring(0, 20);
      return truncated + (firstMessage.length > 20 ? "..." : "");
    }
    return "未命名会话";
  }

  /**
   * 验证会话数据的完整性
   * @param {Object} sessionData - 会话数据
   * @returns {Object} 验证结果 {valid: boolean, errors: string[]}
   */
  validateSessionData(sessionData) {
    const errors = [];

    // 必需字段检查
    if (!sessionData.session_type) {
      errors.push("session_type 是必需的");
    }

    if (!sessionData.data_scope) {
      errors.push("data_scope 是必需的");
    }

    // 根据 data_scope 检查必需字段
    if (sessionData.data_scope === "course") {
      if (!sessionData.course_id) {
        errors.push("当 data_scope 为 course 时，course_id 是必需的");
      }
      if (!sessionData.course_name) {
        errors.push("当 data_scope 为 course 时，course_name 是必需的");
      }
    } else if (sessionData.data_scope === "course_material") {
      if (!sessionData.course_material_id) {
        errors.push(
          "当 data_scope 为 course_material 时，course_material_id 是必需的"
        );
      }
      if (!sessionData.course_material_name) {
        errors.push(
          "当 data_scope 为 course_material 时，course_material_name 是必需的"
        );
      }
    }

    return {
      valid: errors.length === 0,
      errors,
    };
  }

  /**
   * 创建新会话
   * @param {Object} sessionConfig - 会话配置
   * @returns {Promise<Object>} 创建的会话对象
   */
  async createSession(sessionConfig) {
    // 生成会话ID
    const sessionId = this.generateSessionId();

    // 构建会话数据
    const sessionData = {
      session_id: sessionId,
      session_type: sessionConfig.session_type || "course_chat",
      data_scope: sessionConfig.data_scope,
      mode: sessionConfig.mode || "query",
      frontend_session_chat_history: [],
      chatSummaryBuffer: {
        messages: [],
        summary: null,
        token_count: 0,
      },
      ...sessionConfig,
    };

    // 生成标题
    sessionData.title = this.generateSessionTitle(
      sessionData.session_type,
      sessionData.data_scope,
      sessionData.course_name,
      sessionData.course_material_name,
      sessionConfig.firstMessage
    );

    // 验证数据完整性
    const validation = this.validateSessionData(sessionData);
    if (!validation.valid) {
      throw new Error(`会话数据验证失败: ${validation.errors.join(", ")}`);
    }

    // 保存到数据库
    await this.dbHelper.createSession(sessionData);

    console.log("新会话创建成功:", sessionData);
    return sessionData;
  }

  /**
   * 查找或创建会话（基于 data_scope 的唯一性约束）
   * @param {Object} sessionConfig - 会话配置
   * @returns {Promise<Object>} 找到的或新创建的会话对象
   */
  async findOrCreateSession(sessionConfig) {
    try {
      const { data_scope } = sessionConfig;

      // 确定查找的ID
      let scopeId;
      if (data_scope === "course") {
        scopeId = sessionConfig.course_id;
      } else if (data_scope === "course_material") {
        scopeId = sessionConfig.course_material_id;
      } else {
        throw new Error(`不支持的 data_scope: ${data_scope}`);
      }

      if (!scopeId) {
        throw new Error(`缺少必需的ID字段`);
      }

      // 查找现有会话
      const existingSession = await this.dbHelper.findSessionByDataScope(
        data_scope,
        scopeId
      );

      if (existingSession) {
        console.log("找到现有会话:", existingSession);
        this.currentSession = existingSession;
        return existingSession;
      }

      // 创建新会话
      const newSession = await this.createSession(sessionConfig);
      this.currentSession = newSession;
      return newSession;
    } catch (error) {
      console.error("查找或创建会话失败:", error);
      throw new Error(`会话操作失败: ${error.message}`);
    }
  }

  /**
   * 加载会话
   * @param {string} sessionId - 会话ID
   * @returns {Promise<Object|null>}
   */
  async loadSession(sessionId) {
    const session = await this.dbHelper.getSession(sessionId);
    if (session) {
      this.currentSession = session;
      console.log("会话加载成功:", session);
    }
    return session;
  }

  /**
   * 获取当前活跃会话
   * @returns {Object|null}
   */
  getCurrentSession() {
    return this.currentSession;
  }

  /**
   * 设置当前活跃会话
   * @param {Object} session - 会话对象
   */
  setCurrentSession(session) {
    this.currentSession = session;
  }

  /**
   * 添加聊天消息到当前会话
   * @param {string} role - 角色 ('user' 或 'assistant')
   * @param {string} content - 消息内容
   * @returns {Promise<boolean>}
   */
  async addChatMessage(role, content) {
    if (!this.currentSession) {
      throw new Error("没有活跃的会话");
    }

    const message = {
      role,
      content,
      timestamp: Date.now(),
    };

    // 添加到聊天历史
    this.currentSession.frontend_session_chat_history.push(message);

    // 同时添加到 chatSummaryBuffer 的 messages 中
    if (!this.currentSession.chatSummaryBuffer) {
      this.currentSession.chatSummaryBuffer = {
        messages: [],
        summary: null,
        token_count: 0,
      };
    }

    // 添加到 chatSummaryBuffer.messages（不包含timestamp，符合ChatMessage格式）
    this.currentSession.chatSummaryBuffer.messages.push({
      role,
      content,
    });

    // 更新数据库
    await this.dbHelper.updateSession(this.currentSession.session_id, {
      frontend_session_chat_history:
        this.currentSession.frontend_session_chat_history,
      chatSummaryBuffer: this.currentSession.chatSummaryBuffer,
    });

    console.log(
      `添加消息到会话，当前chatSummaryBuffer包含 ${this.currentSession.chatSummaryBuffer.messages.length} 条消息`
    );
    return true;
  }

  /**
   * 更新当前会话的 ChatSummaryBuffer
   * @param {Object} chatSummaryBuffer - 新的 ChatSummaryBuffer
   * @returns {Promise<boolean>}
   */
  async updateChatSummaryBuffer(chatSummaryBuffer) {
    if (!this.currentSession) {
      throw new Error("没有活跃的会话");
    }

    this.currentSession.chatSummaryBuffer = chatSummaryBuffer;

    // 更新数据库
    await this.dbHelper.updateSession(this.currentSession.session_id, {
      chatSummaryBuffer,
    });

    return true;
  }

  /**
   * 清空当前会话的聊天历史
   * @returns {Promise<boolean>}
   */
  async clearCurrentSessionHistory() {
    if (!this.currentSession) {
      throw new Error("没有活跃的会话");
    }

    // 清空本地数据
    this.currentSession.frontend_session_chat_history = [];
    this.currentSession.chatSummaryBuffer = {
      messages: [],
      summary: null,
      token_count: 0,
    };

    // 更新数据库
    await this.dbHelper.clearSessionHistory(this.currentSession.session_id);

    return true;
  }

  /**
   * 获取会话列表
   * @param {Object} options - 查询选项
   * @returns {Promise<Array>}
   */
  async getSessionList(options = {}) {
    return await this.dbHelper.listSessions(options);
  }

  /**
   * 删除会话
   * @param {string} sessionId - 会话ID
   * @returns {Promise<boolean>}
   */
  async deleteSession(sessionId) {
    // 如果删除的是当前会话，清空当前会话
    if (this.currentSession && this.currentSession.session_id === sessionId) {
      this.currentSession = null;
    }

    return await this.dbHelper.deleteSession(sessionId);
  }

  /**
   * 搜索会话
   * @param {string} keyword - 搜索关键词
   * @returns {Promise<Array>}
   */
  async searchSessions(keyword) {
    return await this.dbHelper.searchSessionsByTitle(keyword);
  }

  /**
   * 构建RAG查询请求数据
   * @param {string} question - 用户问题
   * @param {string} mode - 聊天模式
   * @returns {Object} 查询请求数据
   */
  buildQueryRequest(question, mode = "query") {
    if (!this.currentSession) {
      throw new Error("没有活跃的会话");
    }

    const queryData = {
      question,
      mode,
      session_id: this.currentSession.session_id,
      data_scope: this.currentSession.data_scope,
      chatSummaryBuffer: this.currentSession.chatSummaryBuffer,
      // 为了向后兼容，也传递 chat_memory
      chat_memory: this.currentSession.chatSummaryBuffer,
    };

    // 根据 data_scope 添加相应的参数
    if (this.currentSession.data_scope === "course") {
      queryData.course_id = this.currentSession.course_id;
      queryData.course_name = this.currentSession.course_name;
    } else if (this.currentSession.data_scope === "course_material") {
      queryData.course_material_id = this.currentSession.course_material_id;
      queryData.course_material_name = this.currentSession.course_material_name;
    }

    console.log(
      "构建查询请求，chatSummaryBuffer:",
      this.currentSession.chatSummaryBuffer
    );
    return queryData;
  }

  /**
   * 处理RAG查询响应
   * @param {Object} response - RAG查询响应
   * @returns {Promise<void>}
   */
  async handleQueryResponse(response) {
    if (!this.currentSession) {
      throw new Error("没有活跃的会话");
    }

    // 更新 ChatSummaryBuffer（如果有）
    if (response.chat_memory) {
      await this.updateChatSummaryBuffer(response.chat_memory);
    }
  }
}

// 创建全局实例
window.sessionManager = new SessionManager();

// 导出类（如果使用模块系统）
if (typeof module !== "undefined" && module.exports) {
  module.exports = SessionManager;
}

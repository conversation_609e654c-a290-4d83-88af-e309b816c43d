/**
 * Visit Session 管理模块
 * 实现浏览器标签页会话管理和30天过期数据自动清理机制
 */

class VisitSessionManager {
  constructor() {
    this.visitSessionKey = "chat_visit_session";
    this.lastCleanupKey = "chat_last_cleanup";
    this.cleanupIntervalDays = 30;
    this.dbHelper = window.indexedDBHelper;
    this.isInitialized = false;
  }

  /**
   * 初始化 Visit Session 管理
   */
  async init() {
    if (this.isInitialized) {
      return;
    }

    try {
      // 检查是否是新的 visit session
      const isNewVisitSession = this.checkAndCreateVisitSession();

      if (isNewVisitSession) {
        console.log("新的 Visit Session 开始");

        // 执行垃圾清理
        await this.performCleanupIfNeeded();
      }

      // 绑定页面卸载事件
      this.bindUnloadEvents();

      // 绑定存储事件（跨标签页通信）
      this.bindStorageEvents();

      this.isInitialized = true;
      console.log("Visit Session 管理器初始化完成");
    } catch (error) {
      console.error("Visit Session 管理器初始化失败:", error);
    }
  }

  /**
   * 检查并创建 Visit Session
   * @returns {boolean} 是否是新的 visit session
   */
  checkAndCreateVisitSession() {
    const existingSession = sessionStorage.getItem(this.visitSessionKey);

    if (!existingSession) {
      // 创建新的 visit session
      const visitSessionId = this.generateVisitSessionId();
      sessionStorage.setItem(this.visitSessionKey, visitSessionId);

      // 在 localStorage 中记录活跃的 visit session
      this.addActiveVisitSession(visitSessionId);

      return true;
    }

    return false;
  }

  /**
   * 生成 Visit Session ID
   * @returns {string}
   */
  generateVisitSessionId() {
    return (
      "visit_" + Date.now() + "_" + Math.random().toString(36).substring(2, 11)
    );
  }

  /**
   * 添加活跃的 Visit Session
   * @param {string} sessionId
   */
  addActiveVisitSession(sessionId) {
    const activeSessions = this.getActiveVisitSessions();
    activeSessions[sessionId] = Date.now();
    localStorage.setItem(
      "active_visit_sessions",
      JSON.stringify(activeSessions)
    );
  }

  /**
   * 移除活跃的 Visit Session
   * @param {string} sessionId
   */
  removeActiveVisitSession(sessionId) {
    const activeSessions = this.getActiveVisitSessions();
    delete activeSessions[sessionId];
    localStorage.setItem(
      "active_visit_sessions",
      JSON.stringify(activeSessions)
    );
  }

  /**
   * 获取活跃的 Visit Sessions
   * @returns {Object}
   */
  getActiveVisitSessions() {
    try {
      const sessions = localStorage.getItem("active_visit_sessions");
      return sessions ? JSON.parse(sessions) : {};
    } catch (error) {
      console.error("获取活跃会话失败:", error);
      return {};
    }
  }

  /**
   * 绑定页面卸载事件
   */
  bindUnloadEvents() {
    // 页面卸载时清理当前 visit session
    window.addEventListener("beforeunload", () => {
      this.cleanupCurrentVisitSession();
    });

    // 页面可见性变化时的处理
    document.addEventListener("visibilitychange", () => {
      if (document.hidden) {
        // 页面隐藏时，延迟清理（防止快速切换标签页）
        setTimeout(() => {
          if (document.hidden) {
            this.cleanupCurrentVisitSession();
          }
        }, 5000); // 5秒后清理
      }
    });
  }

  /**
   * 绑定存储事件（跨标签页通信）
   */
  bindStorageEvents() {
    window.addEventListener("storage", (event) => {
      if (event.key === "active_visit_sessions") {
        // 其他标签页的 visit session 状态发生变化
        this.handleVisitSessionChange();
      }
    });
  }

  /**
   * 处理 Visit Session 变化
   */
  handleVisitSessionChange() {
    const activeSessions = this.getActiveVisitSessions();
    const currentSessionId = sessionStorage.getItem(this.visitSessionKey);

    // 检查当前会话是否仍然活跃
    if (currentSessionId && !activeSessions[currentSessionId]) {
      console.log("当前 Visit Session 已被其他标签页清理");
    }
  }

  /**
   * 清理当前 Visit Session
   */
  cleanupCurrentVisitSession() {
    const currentSessionId = sessionStorage.getItem(this.visitSessionKey);
    if (currentSessionId) {
      this.removeActiveVisitSession(currentSessionId);
      console.log("清理当前 Visit Session:", currentSessionId);
    }
  }

  /**
   * 执行垃圾清理（如果需要）
   */
  async performCleanupIfNeeded() {
    try {
      const lastCleanup = localStorage.getItem(this.lastCleanupKey);
      const now = Date.now();
      const cleanupInterval = this.cleanupIntervalDays * 24 * 60 * 60 * 1000; // 转换为毫秒

      if (!lastCleanup || now - parseInt(lastCleanup) > cleanupInterval) {
        console.log("开始执行过期数据清理...");

        const deletedCount = await this.dbHelper.cleanupExpiredSessions(
          this.cleanupIntervalDays
        );

        // 更新最后清理时间
        localStorage.setItem(this.lastCleanupKey, now.toString());

        console.log(`垃圾清理完成，删除了 ${deletedCount} 个过期会话`);

        // 可以触发一个自定义事件通知其他组件
        window.dispatchEvent(
          new CustomEvent("sessionsCleanedUp", {
            detail: { deletedCount },
          })
        );
      }
    } catch (error) {
      console.error("垃圾清理失败:", error);
    }
  }

  /**
   * 手动触发垃圾清理
   * @param {number} days 清理多少天前的数据，默认30天
   * @returns {Promise<number>} 删除的会话数量
   */
  async manualCleanup(days = 30) {
    try {
      console.log(`手动清理 ${days} 天前的过期数据...`);

      const deletedCount = await this.dbHelper.cleanupExpiredSessions(days);

      // 更新最后清理时间
      localStorage.setItem(this.lastCleanupKey, Date.now().toString());

      console.log(`手动清理完成，删除了 ${deletedCount} 个过期会话`);

      // 触发清理完成事件
      window.dispatchEvent(
        new CustomEvent("sessionsCleanedUp", {
          detail: { deletedCount, manual: true },
        })
      );

      return deletedCount;
    } catch (error) {
      console.error("手动清理失败:", error);
      throw error;
    }
  }

  /**
   * 获取清理统计信息
   * @returns {Object}
   */
  getCleanupStats() {
    const lastCleanup = localStorage.getItem(this.lastCleanupKey);
    const activeSessions = this.getActiveVisitSessions();

    return {
      lastCleanupTime: lastCleanup ? new Date(parseInt(lastCleanup)) : null,
      activeVisitSessionsCount: Object.keys(activeSessions).length,
      nextCleanupDue: lastCleanup
        ? new Date(
            parseInt(lastCleanup) +
              this.cleanupIntervalDays * 24 * 60 * 60 * 1000
          )
        : new Date(),
      cleanupIntervalDays: this.cleanupIntervalDays,
    };
  }

  /**
   * 设置清理间隔
   * @param {number} days 天数
   */
  setCleanupInterval(days) {
    this.cleanupIntervalDays = days;
    console.log(`清理间隔设置为 ${days} 天`);
  }

  /**
   * 检查是否有活跃的标签页
   * @returns {boolean}
   */
  hasActiveVisitSessions() {
    const activeSessions = this.getActiveVisitSessions();
    return Object.keys(activeSessions).length > 0;
  }

  /**
   * 获取当前 Visit Session ID
   * @returns {string|null}
   */
  getCurrentVisitSessionId() {
    return sessionStorage.getItem(this.visitSessionKey);
  }
}

// 创建全局实例
window.visitSessionManager = new VisitSessionManager();

// 导出类（如果使用模块系统）
if (typeof module !== "undefined" && module.exports) {
  module.exports = VisitSessionManager;
}

// 自动初始化（在页面加载时）
document.addEventListener("DOMContentLoaded", async () => {
  try {
    await window.visitSessionManager.init();
  } catch (error) {
    console.error("Visit Session 管理器自动初始化失败:", error);
  }
});
